import React from 'react';
import { PageLayout, Tooltip, PrimaryButton } from '../components/ui';
import { CombinedFileUploadHelp } from '../components/chat/CombinedFileUploadHelp';

const TooltipTest: React.FC = () => {
  return (
    <PageLayout 
      title="Tooltip Test" 
      containerWidth="wide"
    >
      <div className="space-y-8 p-8">
        <h1 className="text-2xl font-bold">Tooltip Functionality Test</h1>
        
        {/* Test 1: Basic tooltip with hover */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Test 1: Hover Tooltip</h2>
          <Tooltip
            content="This is a simple hover tooltip"
            placement="top"
            trigger="hover"
          >
            <PrimaryButton>Hover me</PrimaryButton>
          </Tooltip>
        </div>

        {/* Test 2: Click tooltip */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Test 2: Click Tooltip</h2>
          <Tooltip
            content="This is a click tooltip"
            placement="top"
            trigger="click"
          >
            <PrimaryButton>Click me</PrimaryButton>
          </Tooltip>
        </div>

        {/* Test 3: Both triggers */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Test 3: Both Triggers</h2>
          <Tooltip
            content="This tooltip responds to both hover and click"
            placement="top"
            trigger="both"
          >
            <PrimaryButton>Hover or Click me</PrimaryButton>
          </Tooltip>
        </div>

        {/* Test 4: File upload help tooltip (the actual problematic one) */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Test 4: File Upload Help Tooltip</h2>
          <Tooltip
            content={<CombinedFileUploadHelp isMobile={window.innerWidth <= 640} />}
            placement="top"
            trigger="both"
            className="bg-gray-900 text-white"
            maxWidth="320px"
          >
            <button
              type="button"
              className="p-1 min-h-[44px] min-w-[44px] flex items-center justify-center text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors rounded-lg hover:bg-jobblogg-card focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20"
              aria-label="Hjelp for filopplasting og kamera"
            >
              <svg 
                className="w-4 h-4 text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
            </button>
          </Tooltip>
          <span className="text-xs text-jobblogg-text-muted ml-2">
            Hjelp for filopplasting og kamera
          </span>
        </div>

        {/* Test 5: Different placements */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Test 5: Different Placements</h2>
          <div className="flex gap-4 flex-wrap">
            <Tooltip content="Top placement" placement="top" trigger="both">
              <PrimaryButton>Top</PrimaryButton>
            </Tooltip>
            <Tooltip content="Bottom placement" placement="bottom" trigger="both">
              <PrimaryButton>Bottom</PrimaryButton>
            </Tooltip>
            <Tooltip content="Left placement" placement="left" trigger="both">
              <PrimaryButton>Left</PrimaryButton>
            </Tooltip>
            <Tooltip content="Right placement" placement="right" trigger="both">
              <PrimaryButton>Right</PrimaryButton>
            </Tooltip>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-jobblogg-card p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Test Instructions:</h3>
          <ul className="space-y-1 text-sm">
            <li>• On desktop: Try hovering over the buttons</li>
            <li>• On mobile: Try tapping the buttons</li>
            <li>• Check browser console for debug messages</li>
            <li>• Test 4 should match the problematic tooltip from chat</li>
          </ul>
        </div>
      </div>
    </PageLayout>
  );
};

export default TooltipTest;
